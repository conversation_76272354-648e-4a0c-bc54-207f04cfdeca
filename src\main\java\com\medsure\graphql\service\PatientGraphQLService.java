package com.medsure.graphql.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import org.dataloader.DataLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.medsure.dao.PatientDAO;
import com.medsure.factory.WatchRxFactory;
import com.medsure.graphql.model.PatientCGInfoGraphQL;
import com.medsure.graphql.model.PatientGCInfosGraphQL;
import com.medsure.model.WatchrxPatient;
import com.medsure.service.PatientService;
import com.medsure.ui.entity.caregiver.response.PatientCGInfo;
import com.medsure.ui.entity.caregiver.response.PatientGCInfos;

import graphql.schema.DataFetchingEnvironment;

@Service
public class PatientGraphQLService {
    
    private static final Logger log = LoggerFactory.getLogger(PatientGraphQLService.class);
    
    @Autowired
    private PatientService patientService;
    
    @Autowired
    private PatientDAO patientDAO;
    
    /**
     * GraphQL optimized method to fetch patients for nurse with selective field loading
     */
    public PatientGCInfosGraphQL getPatientsForNurse(Long orgId, String careGiverId, String roleType, 
                                                    Integer pageNumber, Integer pageSize, 
                                                    DataFetchingEnvironment environment) {
        
        log.info("GraphQL getPatientsForNurse called with orgId: {}, careGiverId: {}, roleType: {}", 
                orgId, careGiverId, roleType);
        
        // Get requested fields from GraphQL query
        Set<String> requestedFields = getRequestedFields(environment);
        
        // Use existing service method but optimize based on requested fields
        PatientGCInfos restResponse = patientService.getAllPatientsByOrgIdAndUserId(
            orgId, Long.valueOf(careGiverId), getRoleTypeId(roleType), pageNumber, pageSize);
        
        // Convert to GraphQL model with selective field population
        return convertToGraphQLModel(restResponse, requestedFields);
    }
    
    /**
     * Convert REST model to GraphQL model with selective field population
     */
    private PatientGCInfosGraphQL convertToGraphQLModel(PatientGCInfos restResponse, Set<String> requestedFields) {
        PatientGCInfosGraphQL graphqlResponse = new PatientGCInfosGraphQL();
        
        // Always populate basic response fields
        graphqlResponse.setResponseCode(restResponse.getResponseCode());
        graphqlResponse.setResponseMessage(restResponse.getResponseMessage());
        graphqlResponse.setStatus(restResponse.getStatus());
        graphqlResponse.setResponseType(restResponse.getResponseType());
        graphqlResponse.setNurseId(restResponse.getNurseId());
        
        // Conditionally populate count if requested
        if (requestedFields.contains("count")) {
            graphqlResponse.setCount(restResponse.getCount());
        }
        
        // Conditionally populate patient info if requested
        if (requestedFields.contains("patientInfo") && restResponse.getPatientInfo() != null) {
            List<PatientCGInfoGraphQL> graphqlPatients = new ArrayList<>();
            
            for (PatientCGInfo restPatient : restResponse.getPatientInfo()) {
                PatientCGInfoGraphQL graphqlPatient = convertPatientToGraphQL(restPatient, requestedFields, environment);
                graphqlPatients.add(graphqlPatient);
            }
            
            graphqlResponse.setPatientInfo(graphqlPatients);
        }
        
        return graphqlResponse;
    }
    
    /**
     * Convert individual patient with selective field population
     */
    private PatientCGInfoGraphQL convertPatientToGraphQL(PatientCGInfo restPatient, Set<String> requestedFields,
                                                        DataFetchingEnvironment environment) {
        PatientCGInfoGraphQL graphqlPatient = new PatientCGInfoGraphQL();
        
        // Only populate requested fields to optimize response size
        if (requestedFields.contains("patientName")) {
            graphqlPatient.setPatientName(restPatient.getPatientName());
        }
        if (requestedFields.contains("patientId")) {
            graphqlPatient.setPatientId(restPatient.getPatientId());
        }
        if (requestedFields.contains("phone")) {
            graphqlPatient.setPhone(restPatient.getPhone());
        }
        if (requestedFields.contains("address")) {
            graphqlPatient.setAddress(restPatient.getAddress());
        }
        if (requestedFields.contains("dob")) {
            graphqlPatient.setDob(restPatient.getDob());
        }
        if (requestedFields.contains("visitReason")) {
            graphqlPatient.setVisitReason(restPatient.getVisitReason());
        }
        if (requestedFields.contains("gpsStatus")) {
            graphqlPatient.setGpsStatus(restPatient.getGpsStatus());
        }
        if (requestedFields.contains("trackingStatus")) {
            graphqlPatient.setTrackingStatus(restPatient.getTrackingStatus());
        }
        if (requestedFields.contains("radius")) {
            graphqlPatient.setRadius(restPatient.getRadius());
        }
        if (requestedFields.contains("latLong")) {
            graphqlPatient.setLatLong(restPatient.getLatLong());
        }
        if (requestedFields.contains("trackLatLong")) {
            graphqlPatient.setTrackLatLong(restPatient.getTrackLatLong());
        }
        if (requestedFields.contains("reachableStatus")) {
            graphqlPatient.setReachableStatus(restPatient.getReachableStatus());
        }
        if (requestedFields.contains("connectingDevice")) {
            graphqlPatient.setConnectingDevice(restPatient.getConnectingDevice());
        }
        if (requestedFields.contains("imeiNo")) {
            graphqlPatient.setImeiNo(restPatient.getImeiNo());
        }
        if (requestedFields.contains("image")) {
            graphqlPatient.setImage(restPatient.getImage());
        }
        if (requestedFields.contains("visitShift")) {
            graphqlPatient.setVisitShift(restPatient.getVisitShift());
        }
        if (requestedFields.contains("programs")) {
            // Use DataLoader for optimized program loading
            DataLoader<Long, Map<String, Object>> programLoader =
                environment.getDataLoader("patientPrograms");
            if (programLoader != null) {
                CompletableFuture<Map<String, Object>> programsFuture =
                    programLoader.load(Long.valueOf(restPatient.getPatientId()));
                // For now, we'll use the existing data, but in a real async implementation
                // you'd handle the CompletableFuture properly
                graphqlPatient.setPrograms(restPatient.getPrograms());
            } else {
                graphqlPatient.setPrograms(restPatient.getPrograms());
            }
        }
        
        return graphqlPatient;
    }
    
    /**
     * Extract requested fields from GraphQL DataFetchingEnvironment
     */
    private Set<String> getRequestedFields(DataFetchingEnvironment environment) {
        return environment.getSelectionSet().getFields().keySet();
    }
    
    /**
     * Convert role type string to role type ID
     */
    private Long getRoleTypeId(String roleType) {
        if (roleType != null && roleType.equalsIgnoreCase("physician")) {
            return 3L;
        }
        return 5L; // Default to nurse role
    }
}
