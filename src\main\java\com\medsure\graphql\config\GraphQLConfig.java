package com.medsure.graphql.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.graphql.execution.RuntimeWiringConfigurer;
import org.springframework.graphql.server.WebGraphQlConfigurer;

import com.medsure.graphql.interceptor.GraphQLAuthenticationInterceptor;

import graphql.scalars.ExtendedScalars;

@Configuration
public class GraphQLConfig {

    @Autowired
    private GraphQLAuthenticationInterceptor authenticationInterceptor;

    @Bean
    public RuntimeWiringConfigurer runtimeWiringConfigurer() {
        return wiringBuilder -> wiringBuilder
            .scalar(ExtendedScalars.GraphQLLong)
            .scalar(ExtendedScalars.Json);
    }

    @Bean
    public WebGraphQlConfigurer webGraphQlConfigurer() {
        return configurer -> configurer
            .interceptor(authenticationInterceptor);
    }
}
