<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.namespace</groupId>
	<artifactId>WatchRx</artifactId>
	<packaging>war</packaging>
	<version>3.4</version>
	<name>WatchRx</name>
	<url>https://repo1.maven.org/maven2/</url>
	<properties>
		<configuration-app.deploy.promote>false</configuration-app.deploy.promote>
		<configuration-disableUpdateCheck>false</configuration-disableUpdateCheck>
		<roo.version>1.2.1.RELEASE</roo.version>
		<spring.version>5.3.31</spring.version>
		<java.version>17</java.version>
		<slf4j.version>1.8.0-beta4</slf4j.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<!-- <gae.version>1.9.42</gae.version> -->
		<gae.version>2.0.4</gae.version>
		<spring-security.version>5.8.9</spring-security.version>
		<guava.ver>33.0.0-jre</guava.ver>
		<bootstrap.ver>3.3.2</bootstrap.ver>
		<jquery.ver>2.1.3</jquery.ver>
		<bootbox.ver>4.4.0</bootbox.ver>
		<gson.ver>2.10.1</gson.ver>
		<jquery.ui.ver>1.11.3</jquery.ui.ver>
		<velocity.ver>1.7</velocity.ver>
		<velocity.tools.ver>2.0</velocity.tools.ver>
		<jstl.ver>1.2</jstl.ver>
		<taglibs.ver>1.1.2</taglibs.ver>
		<tiles.ver>2.2.2</tiles.ver>
		<html5shiv.ver>3.7.2</html5shiv.ver>
		<respond.ver>1.4.2</respond.ver>
		<jackson.ver>1.8.2</jackson.ver>
		<validation.ver>2.0.0.Final</validation.ver>
		<mysql.version>8.0.33</mysql.version>
		<commons-dbcp.version>1.4</commons-dbcp.version>
		<hibernate.version>4.3.6.Final</hibernate.version>
		<aspectj.version>1.9.4</aspectj.version>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
	</properties>
	<repositories>
		<repository>
			<id>spring-maven-release</id>
			<name>Spring Maven Release Repository</name>
			<url>https://maven.springframework.org/release</url>
		</repository>
		<repository>
			<id>spring-maven-milestone</id>
			<name>Spring Maven Milestone Repository</name>
			<url>https://maven.springframework.org/milestone</url>
		</repository>
		<repository>
			<id>spring-roo-repository</id>
			<name>Spring Roo Repository</name>
			<url>https://spring-roo-repository.springsource.org/release</url>
		</repository>
		<repository>
			<id>maven-gae-plugin-repo</id>
			<url>https://maven-gae-plugin.googlecode.com/svn/repository</url>
			<name>maven-gae-plugin repository</name>
		</repository>
		<repository>
			<id>JBoss Repo</id>
			<url>
				https://repository.jboss.org/nexus/content/repositories/releases</url>
			<name>JBoss Repo</name>
		</repository>
		<!-- objectify-appengine -->
		<repository>
			<id>objectify-appengine</id>
			<url>https://objectify-appengine.googlecode.com/svn/maven</url>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>spring-maven-release</id>
			<name>Spring Maven Release Repository</name>
			<url>https://maven.springframework.org/release</url>
		</pluginRepository>
		<pluginRepository>
			<id>spring-maven-milestone</id>
			<name>Spring Maven Milestone Repository</name>
			<url>https://maven.springframework.org/milestone</url>
		</pluginRepository>
		<pluginRepository>
			<id>maven-gae-plugin-repo</id>
			<url>https://maven-gae-plugin.googlecode.com/svn/repository</url>
			<name>maven-gae-plugin repository</name>
		</pluginRepository>
	</pluginRepositories>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>libraries-bom</artifactId>
				<version>26.59.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<!--
		https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.4</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/org.apache.commons/commons-compress -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
			<version>1.25.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.yaml/snakeyaml -->
		<dependency>
			<groupId>org.yaml</groupId>
			<artifactId>snakeyaml</artifactId>
			<version>2.2</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<!--<version>2.7</version>-->
			<version>2.14.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.json/json -->
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20231013</version>
		</dependency>


		<!--
		https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-core -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.16.1</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.16.1</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/com.fasterxml.jackson.module/jackson-modules-java8 -->
		<dependency>
			<groupId>com.fasterxml.jackson.module</groupId>
			<artifactId>jackson-modules-java8</artifactId>
			<version>2.16.1</version>
			<type>pom</type>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava.ver}</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>failureaccess</artifactId>
			<version>1.0.2</version>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>${aspectj.version}</version>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>${aspectj.version}</version>
		</dependency>
		<!-- General dependencies for standard applications -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.2</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
			<version>${slf4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>${slf4j.version}</version>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>4.0.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.14.0</version>
		</dependency>

		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>mail</artifactId>
			<version>1.5.0-b01</version>
		</dependency>


		<!-- Spring dependencies -->


		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
			<version>${spring.version}</version>
		</dependency>


		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>${spring.version}</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>${spring.version}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aop</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
			<version>${spring.version}</version>
		</dependency>

		<!-- <dependency> <groupId>com.google.appengine</groupId>
		<artifactId>appengine-endpoints</artifactId> 
			<version>${gae.version}</version> </dependency> -->
		<dependency>
			<groupId>com.google.endpoints</groupId>
			<artifactId>endpoints-framework</artifactId>
			<version>2.2.2</version>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.google.appengine</groupId>
			<artifactId>appengine-api-1.0-sdk</artifactId>
			<version>${gae.version}</version>
		</dependency>
		<dependency>
			<groupId>com.google.appengine</groupId>
			<artifactId>appengine-testing</artifactId>
			<version>${gae.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.google.appengine</groupId>
			<artifactId>appengine-api-stubs</artifactId>
			<version>${gae.version}</version>
			<scope>test</scope>
		</dependency>
		<!--	<dependency>
			<groupId>com.google.appengine</groupId>
			<artifactId>appengine-api-labs</artifactId>
			<version>${gae.version}</version>
			<scope>test</scope>
		</dependency>-->
		<dependency>
			<groupId>javax.persistence</groupId>
			<artifactId>persistence-api</artifactId>
			<version>1.0.2</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.hibernate.orm/hibernate-core -->
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
			<version>5.4.28.Final</version>
		</dependency>


		<dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
			<version>1.0.0.GA</version>
		</dependency>
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib-nodep</artifactId>
			<version>3.3.0</version>
		</dependency>
		<dependency>
			<groupId>javax.transaction</groupId>
			<artifactId>jta</artifactId>
			<version>1.1</version>
		</dependency>
		<dependency>
			<groupId>commons-pool</groupId>
			<artifactId>commons-pool</artifactId>
			<version>1.6</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>${spring.version}</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
			<version>${spring.version}</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>commons-digester</groupId>
			<artifactId>commons-digester</artifactId>
			<version>2.1</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.5</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>javax.el</groupId>
			<artifactId>el-api</artifactId>
			<version>2.2.1-b04</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>2.12.5</version>
		</dependency>
		<!-- @Inject -->
		<dependency>
			<groupId>javax.inject</groupId>
			<artifactId>javax.inject</artifactId>
			<version>1</version>
		</dependency>

		<dependency>
			<groupId>javax.servlet.jsp</groupId>
			<artifactId>jsp-api</artifactId>
			<version>2.2.1-b03</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.16.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.tiles</groupId>
			<artifactId>tiles-jsp</artifactId>
			<version>${tiles.ver}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-core</artifactId>
			<version>${spring-security.version}</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-config</artifactId>
			<version>${spring-security.version}</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-web</artifactId>
			<version>${spring-security.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-taglibs</artifactId>
			<version>${spring-security.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.14</version>
		</dependency>

		<!-- objectify-appengine -->

		<dependency>
			<groupId>com.googlecode.objectify</groupId>
			<artifactId>objectify</artifactId>
			<version>6.1.0</version>
		</dependency>
		<dependency>
			<groupId>com.googlecode.objectify-appengine-spring</groupId>
			<artifactId>objectify-appengine-spring</artifactId>
			<version>1.1.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.16</version>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>${gson.ver}</version>
		</dependency>

		<dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
			<version>${validation.ver}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.mashape.unirest/unirest-java -->
		<dependency>
			<groupId>com.mashape.unirest</groupId>
			<artifactId>unirest-java</artifactId>
			<version>1.4.9</version>
		</dependency>

		<!-- <dependency> <groupId>com.google.appengine.tools</groupId>
		<artifactId>appengine-gcs-client</artifactId> 
			<version>0.4.4</version> </dependency> -->
		<dependency>
			<groupId>com.google.appengine.tools</groupId>
			<artifactId>appengine-gcs-client</artifactId>
			<version>0.8.3</version>
		</dependency>
		<dependency>
			<groupId>com.ganyo</groupId>
			<artifactId>gcm-server</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.33</version>
		</dependency>
		<dependency>
			<groupId>com.google.cloud.sql</groupId>
			<artifactId>mysql-socket-factory-connector-j-8</artifactId>
			<version>1.15.1</version>
		</dependency>
		<dependency>
			<groupId>com.zaxxer</groupId>
			<artifactId>HikariCP</artifactId>
			<version>5.1.0</version>
		</dependency>
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
			<version>${commons-dbcp.version}</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-entitymanager</artifactId>
			<version>5.4.28.Final</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate.javax.persistence</groupId>
			<artifactId>hibernate-jpa-2.1-api</artifactId>
			<version>1.0.2.Final</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-io</artifactId>
			<version>1.3.2</version>
		</dependency>
		<!-- <dependency> <groupId>com.google.api.client</groupId>
		<artifactId>google-api-client</artifactId> 
			<version>1.4.1-beta</version> </dependency> -->
		<dependency>
			<groupId>com.google.api-client</groupId>
			<artifactId>google-api-client-gson</artifactId>
			<version>2.2.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.http-client</groupId>
			<artifactId>google-http-client</artifactId>
			<version>1.43.3</version>
		</dependency>
		<dependency>
			<groupId>com.google.api-client</groupId>
			<artifactId>google-api-client</artifactId>
			<version>1.32.1</version>
		</dependency>
		<!-- <dependency> <groupId>com.stripe</groupId>
		<artifactId>stripe-java</artifactId> 
			<version>4.9.1</version> </dependency> -->
		<dependency>
			<groupId>com.stripe</groupId>
			<artifactId>stripe-java</artifactId>
			<version>24.10.0-beta.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.maps</groupId>
			<artifactId>google-maps-services</artifactId>
			<version>2.2.0</version>
		</dependency>
		<dependency>
			<groupId>org.w3c</groupId>
			<artifactId>dom</artifactId>
			<version>2.3.0-jaxb-1.0.6</version>
		</dependency>
		<dependency>
			<groupId>org.jxls</groupId>
			<artifactId>jxls-poi</artifactId>
			<version>2.14.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>5.2.5</version>
		</dependency>
		<!-- excel 2007 over -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.2.5</version>
		</dependency>

		<!-- Swagger-ui related dependencies -->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>3.0.0</version>
		</dependency>
		<!--Dependency for swagger ui -->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>3.0.0</version>
		</dependency>
		<!-- end -->
		<dependency>
			<groupId>org.springframework.amqp</groupId>
			<artifactId>spring-rabbit</artifactId>
			<version>3.1.1</version>
		</dependency>
		<dependency>
			<groupId>com.eatthepath</groupId>
			<artifactId>java-otp</artifactId>
			<version>0.4.0</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
			<version>0.9.1</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.projectlombok/lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.30</version>
			<scope>provided</scope>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/org.springframework.data/spring-data-jpa -->
		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-jpa</artifactId>
			<version>2.6.5</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/com.squareup.retrofit2/converter-jackson -->
		<dependency>
			<groupId>com.squareup.retrofit2</groupId>
			<artifactId>converter-jackson</artifactId>
			<version>2.9.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.squareup.retrofit2/retrofit -->
		<dependency>
			<groupId>com.squareup.retrofit2</groupId>
			<artifactId>retrofit</artifactId>
			<version>2.9.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp -->
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>5.0.0-alpha.12</version>
		</dependency>

		<dependency>
			<groupId>com.github.javafaker</groupId>
			<artifactId>javafaker</artifactId>
			<version>1.0.2</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/net.sf.jxls/jxls-core -->
		<dependency>
			<groupId>net.sf.jxls</groupId>
			<artifactId>jxls-core</artifactId>
			<version>1.0.6</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.twilio.sdk/twilio -->
		<dependency>
			<groupId>com.twilio.sdk</groupId>
			<artifactId>twilio</artifactId>
			<version>10.0.0-rc.6</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi.fhir</groupId>
			<artifactId>hapi-fhir-base</artifactId>
			<version>6.10.2</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi.fhir</groupId>
			<artifactId>hapi-fhir-structures-dstu3</artifactId>
			<version>6.10.2</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi.fhir</groupId>
			<artifactId>hapi-fhir-client</artifactId>
			<version>6.10.2</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/com.google.cloud/google-cloud-storage -->
		<dependency>
			<groupId>com.google.cloud</groupId>
			<artifactId>google-cloud-storage</artifactId>
			<version>2.4.1</version>
		</dependency>
		<!--
		https://mvnrepository.com/artifact/com.google.cloud/google-cloud-document-ai -->
		<dependency>
			<groupId>com.google.cloud</groupId>
			<artifactId>google-cloud-document-ai</artifactId>
		</dependency>
		<dependency>
			<groupId>org.xhtmlrenderer</groupId>
			<artifactId>flying-saucer-pdf-itext5</artifactId>
			<version>9.1.22</version>
		</dependency>
		<dependency>
			<groupId>com.lowagie</groupId>
			<artifactId>itext</artifactId>
			<version>2.1.7</version>
		</dependency>

		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itextpdf</artifactId>
			<version>5.5.10</version>
		</dependency>
		<dependency>
			<groupId>com.google.firebase</groupId>
			<artifactId>firebase-admin</artifactId>
			<version>9.3.0</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-api</artifactId>
			<version>0.11.5</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-impl</artifactId>
			<version>0.11.5</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-jackson</artifactId>
			<version>0.11.5</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/io.tryvital/vital-java -->
		<dependency>
			<groupId>io.tryvital</groupId>
			<artifactId>vital-java</artifactId>
			<version>1.2.166</version>
		</dependency>
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>4.2.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.thymeleaf/thymeleaf -->
		<dependency>
			<groupId>org.thymeleaf</groupId>
			<artifactId>thymeleaf</artifactId>
			<version>3.1.3.RELEASE</version>
		</dependency>
		<!-- Flying Saucer PDF -->
		<dependency>
			<groupId>org.xhtmlrenderer</groupId>
			<artifactId>flying-saucer-pdf</artifactId>
			<version>9.1.22</version>
		</dependency>

		<!-- PDF Output Stream Support -->
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-codec</artifactId>
			<version>1.14</version>
		</dependency>

		<dependency>
			<groupId>com.google.cloud</groupId>
			<artifactId>google-cloud-speech</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.auth</groupId>
			<artifactId>google-auth-library-oauth2-http</artifactId>
		</dependency>
		<dependency>
  <groupId>org.springframework</groupId>
  <artifactId>spring-test</artifactId>
  <version>5.3.31</version>
  <scope>test</scope> <!-- or remove 'test' if used in runtime -->
</dependency>
 <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-vertexai</artifactId>
        </dependency>

        <!-- GraphQL Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-graphql</artifactId>
            <version>2.7.18</version>
        </dependency>
        <dependency>
            <groupId>com.graphql-java</groupId>
            <artifactId>graphql-java-extended-scalars</artifactId>
            <version>19.0</version>
        </dependency>
        <dependency>
            <groupId>com.graphql-java</groupId>
            <artifactId>graphql-java-dataloader</artifactId>
            <version>3.2.0</version>
        </dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<version>3.6.1</version>
				<executions>
					<execution>
						<id>copy</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>
								${project.build.directory}/appengine-staging
							</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.3.2</version>
				<!-- <configuration> <webXml>target/web.xml</webXml>
				</configuration> -->
			</plugin>
			<!-- <plugin> <groupId>com.google.appengine</groupId>
			<artifactId>appengine-maven-plugin</artifactId> 
				<version>1.9.42</version> <configuration>
			<enableJarClasses>false</enableJarClasses> 
				<port>8080</port> <address>0.0.0.0</address> <jvmFlags>
			<jvmFlag>-Xdebug</jvmFlag>
			<jvmFlag>-agentlib:jdwp=transport=dt_socket,address=8000,server=y,suspend=y</jvmFlag> 
				</jvmFlags> <disableUpdateCheck>false</disableUpdateCheck> </configuration> 
				</plugin> -->
			<plugin>
				<groupId>com.google.cloud.tools</groupId>
				<artifactId>endpoints-framework-maven-plugin</artifactId>
				<version>2.1.0</version>
				<configuration>
					<hostname>watchrx-1007.appspot.com</hostname>
				</configuration>

			</plugin>
			<plugin>
				<groupId>com.google.cloud.tools</groupId>
				<artifactId>appengine-maven-plugin</artifactId>
				<version>2.5.0</version>
				<configuration>
					<enableJarClasses>false</enableJarClasses>
					<port>8080</port>

					<!-- <jvmFlags> <jvmFlag>-Xdebug</jvmFlag>
					<jvmFlag>-agentlib:jdwp=transport=dt_socket,address=8000,server=y,suspend=y</jvmFlag> 
						</jvmFlags> -->
					<project>
						watchrx-1007
					</project>
					<version>15</version>
					<promote>false</promote>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.5</version>
				<configuration>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.12</version>
				<configuration>
					<printSummary>false</printSummary>
					<redirectTestOutputToFile>true</redirectTestOutputToFile>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.3</version>
				<configuration>
					<descriptorRefs>
						<descriptorRef>jar-with-dependencies</descriptorRef>
					</descriptorRefs>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>2.7</version>
			</plugin>
			<!-- IDE -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-eclipse-plugin</artifactId>
				<version>2.7</version>  <!-- Note 2.8 does not work with AspectJ
				aspect path -->
				<configuration>
					<downloadSources>true</downloadSources>
					<downloadJavadocs>false</downloadJavadocs>
					<wtpversion>2.0</wtpversion>
					<additionalBuildcommands>
						<buildCommand>
							<name>org.eclipse.ajdt.core.ajbuilder</name>
							<arguments>
								<aspectPath>org.springframework.aspects</aspectPath>
							</arguments>
						</buildCommand>
						<buildCommand>
							<name>
								org.springframework.ide.eclipse.core.springbuilder</name>
						</buildCommand>
						<buildCommand>
							<name>
								com.google.appengine.eclipse.core.enhancerbuilder</name>
						</buildCommand>
					</additionalBuildcommands>
					<additionalProjectnatures>
						<projectnature>org.eclipse.ajdt.ui.ajnature</projectnature>
						<projectnature>com.springsource.sts.roo.core.nature</projectnature>
						<projectnature>
							org.springframework.ide.eclipse.core.springnature</projectnature>
						<projectnature>
							com.google.appengine.eclipse.core.gaeNature</projectnature>
					</additionalProjectnatures>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-idea-plugin</artifactId>
				<version>2.2</version>
				<configuration>
					<downloadSources>true</downloadSources>
					<dependenciesAsLibraries>true</dependenciesAsLibraries>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>tomcat-maven-plugin</artifactId>
				<version>1.1</version>
			</plugin>
			<plugin>
				<groupId>org.mortbay.jetty</groupId>
				<artifactId>jetty-maven-plugin</artifactId>
				<version>10.0.0-alpha0</version>
				<configuration>
					<webAppConfig>
						<contextPath>/${project.name}</contextPath>
					</webAppConfig>
				</configuration>
			</plugin>
			<plugin>
				<groupId>net.kindleit</groupId>
				<artifactId>maven-gae-plugin</artifactId>
				<version>0.9.2</version>
				<configuration>
					<unpackVersion>${gae.version}</unpackVersion>
				</configuration>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<!--suppress MavenModelInspection -->
							<goal>unpack</goal>
						</goals>
					</execution>
				</executions>
				<dependencies>
					<dependency>
						<groupId>net.kindleit</groupId>
						<artifactId>gae-runtime</artifactId>
						<version>1.6.4</version>
						<type>pom</type>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
		<finalName>WatchRx</finalName>
		<pluginManagement>
			<plugins>
				<!--This plugin's configuration is used to store Eclipse m2e settings 
					only. It has no influence on the Maven build itself. -->
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>net.kindleit</groupId>
										<artifactId>
											maven-gae-plugin
										</artifactId>
										<versionRange>
											[0.9.2,)
										</versionRange>
										<goals>
											<goal>unpack</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore></ignore>
									</action>
								</pluginExecution>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>
											org.codehaus.mojo
										</groupId>
										<artifactId>
											aspectj-maven-plugin
										</artifactId>
										<versionRange>
											[1.2,)
										</versionRange>
										<goals>
											<goal>test-compile</goal>
											<goal>compile</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore></ignore>
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>
</project>