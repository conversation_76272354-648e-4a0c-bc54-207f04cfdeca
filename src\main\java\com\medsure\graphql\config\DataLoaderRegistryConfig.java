package com.medsure.graphql.config;

import org.dataloader.DataLoaderRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.graphql.execution.DataLoaderRegistrar;

import com.medsure.graphql.dataloader.PatientProgramDataLoader;

@Configuration
public class DataLoaderRegistryConfig {
    
    @Autowired
    private PatientProgramDataLoader patientProgramDataLoader;
    
    @Bean
    public DataLoaderRegistrar dataLoaderRegistrar() {
        return registry -> {
            registry.register("patientPrograms", patientProgramDataLoader.createProgramDataLoader());
        };
    }
}
