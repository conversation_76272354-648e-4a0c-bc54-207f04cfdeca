package com.medsure.config.jwt;

import java.util.Arrays;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@ComponentScan(basePackages = { "com.medsure", "com.ravkoo" })
@Order(1000)
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

	@Autowired
	private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

	@Autowired
	private UserDetailsService jwtUserDetailsService;

	@Autowired
	private JwtRequestFilter jwtRequestFilter;

	@Autowired
	public void configureGlobal(AuthenticationManagerBuilder auth) throws Exception {
		// configure AuthenticationManager so that it knows from where to load
		// user for matching credentials
		// Use BCryptPasswordEncoder
		auth.userDetailsService(jwtUserDetailsService).passwordEncoder(passwordEncoder());
	}

	@Bean
	public PasswordEncoder passwordEncoder() {
		return new BCryptPasswordEncoder();
	}

	@Bean
	@Override
	public AuthenticationManager authenticationManagerBean() throws Exception {
		return super.authenticationManagerBean();
	}

	@Override
	protected void configure(HttpSecurity httpSecurity) throws Exception {
		// We don't need CSRF for this example
		httpSecurity.cors().and().csrf().disable()
				// dont authenticate this particular request
				.authorizeRequests()
				.antMatchers("/voip","/voip/**","/voiceCallBack/**", "/voice/**", "/voiceCallToken/**", "/push-token/**", "/accessToken/**",
						"/accessTokenForWeb/**", "/logVideoCallTimer/**", "/twilioCallBack", "/twilioCallBack/**",
						"/connect/**", "/smartmeter/api/**", "/call/**", "/forwardstatus",
						"/service/int/ravkoo/onboardRavkooUser", "/service/int/ravkoo/getUserNameAndPwd",
						"/service/watch/sendBulkMessage", "/service/int/ravkoo/*", "/authenticate",
						"/api/validateOTPAndSetPassword", "/api/getUserDetailsByImei", "/api/resentOTP", "/service/**",
						"/_ah/api/patientapi/**", "/api/authenticate", "/_ah/api/messaging/**",
						"/_ah/api/registration/**", "/*", "/get*/**", "/api/caregiver/registerUser",
						"/api/caregiver/validateOTPAndSetPassword", "/api/caregiver/resentOTP", "/_ah/api/external/v1/patientCredsByImei",
						"/external/certs/api/**", "/_ah/warmup", "/_ah/api/caregiverapi/v1/CareGiverPasswordReset",
						"/api/caregiver/expirePasswordReset", "/api/cognitalabscapmedic", "/sms", "/api/dexcom/**",
						"/api/tryvital/**", "/api/v2/authenticate", "/api/v2/validateOTPAndSetPassword",
						"/api/v2/resentOTP","/api/v2/caregiver/resentOTP", "/api/v2/patientOtpStatus",
						"/graphiql", "/graphiql/**")
				.permitAll().
				// all other requests need to be authenticated
				anyRequest().authenticated().and().
				// make sure we use stateless session; session won't be used to
				// store user's state.
				exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint).and().sessionManagement()
				.sessionCreationPolicy(SessionCreationPolicy.STATELESS);

		// Add a filter to validate the tokens with every request
		httpSecurity.addFilterBefore(jwtRequestFilter, UsernamePasswordAuthenticationFilter.class);
	}
	
	@Bean
	CorsConfigurationSource corsConfigurationSource() {
	    CorsConfiguration configuration = new CorsConfiguration();
	    configuration.setAllowedOrigins(Arrays.asList("*"));
	    configuration.setAllowedMethods(Arrays.asList("*"));
	    configuration.setAllowedHeaders(Arrays.asList("*"));
	    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
	    source.registerCorsConfiguration("/**", configuration);
	    return source;
	}
	
}
