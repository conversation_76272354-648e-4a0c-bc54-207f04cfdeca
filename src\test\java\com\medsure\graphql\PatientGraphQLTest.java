package com.medsure.graphql;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.graphql.test.tester.GraphQlTester;

import com.medsure.dao.PatientDAO;
import com.medsure.graphql.model.PatientGCInfosGraphQL;
import com.medsure.graphql.service.PatientGraphQLService;
import com.medsure.service.PatientService;
import com.medsure.ui.entity.caregiver.response.PatientCGInfo;
import com.medsure.ui.entity.caregiver.response.PatientGCInfos;

@ExtendWith(MockitoExtension.class)
public class PatientGraphQLTest {
    
    @Mock
    private PatientService patientService;
    
    @Mock
    private PatientDAO patientDAO;
    
    @InjectMocks
    private PatientGraphQLService patientGraphQLService;
    
    private PatientGCInfos mockRestResponse;
    
    @BeforeEach
    void setUp() {
        // Setup mock data
        mockRestResponse = new PatientGCInfos();
        mockRestResponse.setResponseCode("001");
        mockRestResponse.setResponseMessage("Operation successful");
        mockRestResponse.setStatus("success");
        mockRestResponse.setResponseType("1");
        mockRestResponse.setNurseId("123");
        mockRestResponse.setCount(2L);
        
        List<PatientCGInfo> patientList = new ArrayList<>();
        
        // Patient 1
        PatientCGInfo patient1 = new PatientCGInfo();
        patient1.setPatientId("1");
        patient1.setPatientName("John Doe");
        patient1.setPhone("************");
        patient1.setAddress("123 Main St");
        patient1.setDob("1990-01-01");
        patient1.setReachableStatus(true);
        
        Map<String, Object> programs1 = new HashMap<>();
        programs1.put("selectedPrograms", List.of());
        patient1.setPrograms(programs1);
        
        // Patient 2
        PatientCGInfo patient2 = new PatientCGInfo();
        patient2.setPatientId("2");
        patient2.setPatientName("Jane Smith");
        patient2.setPhone("************");
        patient2.setAddress("456 Oak Ave");
        patient2.setDob("1985-05-15");
        patient2.setReachableStatus(false);
        
        Map<String, Object> programs2 = new HashMap<>();
        programs2.put("selectedPrograms", List.of());
        patient2.setPrograms(programs2);
        
        patientList.add(patient1);
        patientList.add(patient2);
        
        mockRestResponse.setPatientInfo(patientList);
    }
    
    @Test
    void testGetPatientsForNurse_Success() {
        // Given
        Long orgId = 123L;
        String careGiverId = "456";
        String roleType = "nurse";
        Integer pageNumber = 0;
        Integer pageSize = 10;
        
        when(patientService.getAllPatientsByOrgIdAndUserId(eq(orgId), eq(456L), eq(5L), eq(pageNumber), eq(pageSize)))
            .thenReturn(mockRestResponse);
        
        // When
        // Note: In a real test, you'd use GraphQlTester, but for unit testing the service layer:
        // PatientGCInfosGraphQL result = patientGraphQLService.getPatientsForNurse(
        //     orgId, careGiverId, roleType, pageNumber, pageSize, mockEnvironment);
        
        // Then
        verify(patientService).getAllPatientsByOrgIdAndUserId(eq(orgId), eq(456L), eq(5L), eq(pageNumber), eq(pageSize));
    }
    
    @Test
    void testGetPatientsForNurse_PhysicianRole() {
        // Given
        Long orgId = 123L;
        String careGiverId = "456";
        String roleType = "physician";
        Integer pageNumber = 0;
        Integer pageSize = 10;
        
        when(patientService.getAllPatientsByOrgIdAndUserId(eq(orgId), eq(456L), eq(3L), eq(pageNumber), eq(pageSize)))
            .thenReturn(mockRestResponse);
        
        // When - would call service method
        // Then
        verify(patientService).getAllPatientsByOrgIdAndUserId(eq(orgId), eq(456L), eq(3L), eq(pageNumber), eq(pageSize));
    }
    
    @Test
    void testRoleTypeConversion() {
        // Test that physician role maps to 3L
        // Test that null/other roles map to 5L (nurse)
        
        // This would be tested in the actual service method calls
        assertTrue(true); // Placeholder for actual role type testing
    }
}
