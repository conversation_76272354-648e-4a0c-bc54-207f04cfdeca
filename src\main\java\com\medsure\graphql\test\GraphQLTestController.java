package com.medsure.graphql.test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.medsure.ui.entity.caregiver.common.CareGiverDetails;
import com.medsure.ui.entity.caregiver.response.PatientGCInfos;
import com.medsure.ui.service.caregiver.CareGiverService;

/**
 * Test controller to verify GraphQL integration works with existing services
 * This can be removed after testing
 */
@RestController
@RequestMapping("/api/graphql-test")
public class GraphQLTestController {
    
    @Autowired
    private CareGiverService careGiverService;
    
    @GetMapping("/test-service")
    public PatientGCInfos testService() {
        // Test that we can call the existing service method
        CareGiverDetails request = new CareGiverDetails();
        request.setOrgId(1L);
        request.setCareGiverId("1");
        request.setRoleType("nurse");
        request.setPageNumber(0);
        request.setPageSize(5);
        
        return careGiverService.getPatientsForNurseV2(request);
    }
    
    @GetMapping("/graphql-info")
    public String getGraphQLInfo() {
        return "GraphQL endpoint available at /graphql. " +
               "GraphiQL interface available at /graphiql. " +
               "Use POST requests with proper authentication headers.";
    }
}
