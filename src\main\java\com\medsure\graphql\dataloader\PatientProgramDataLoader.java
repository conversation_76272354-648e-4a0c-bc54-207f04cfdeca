package com.medsure.graphql.dataloader;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import org.dataloader.BatchLoader;
import org.dataloader.DataLoader;
import org.dataloader.DataLoaderFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.medsure.service.PatientService;

@Component
public class PatientProgramDataLoader {
    
    private static final Logger log = LoggerFactory.getLogger(PatientProgramDataLoader.class);
    
    @Autowired
    private PatientService patientService;
    
    /**
     * Creates a DataLoader for batch loading patient programs
     */
    public DataLoader<Long, Map<String, Object>> createProgramDataLoader() {
        BatchLoader<Long, Map<String, Object>> batchLoader = patientIds -> {
            log.debug("Batch loading programs for {} patients", patientIds.size());
            
            return CompletableFuture.supplyAsync(() -> {
                try {
                    // Batch load programs for all patient IDs
                    return patientIds.stream()
                        .collect(Collectors.toMap(
                            patientId -> patientId,
                            patientId -> {
                                try {
                                    // This would ideally be optimized to batch fetch all programs at once
                                    // For now, we'll use the existing method but could be enhanced
                                    return getPatientProgramsOptimized(patientId);
                                } catch (Exception e) {
                                    log.error("Error loading programs for patient {}", patientId, e);
                                    return Map.of(); // Return empty map on error
                                }
                            }
                        ));
                } catch (Exception e) {
                    log.error("Error in batch loading patient programs", e);
                    throw new RuntimeException("Failed to batch load patient programs", e);
                }
            });
        };
        
        return DataLoaderFactory.newDataLoader(batchLoader);
    }
    
    /**
     * Optimized method to get patient programs
     * This could be enhanced to batch fetch from database
     */
    private Map<String, Object> getPatientProgramsOptimized(Long patientId) {
        try {
            // Use reflection to call the private method from PatientService
            // In a real implementation, you'd want to expose this as a public method
            // or create a batch version
            java.lang.reflect.Method method = patientService.getClass()
                .getDeclaredMethod("getPatientPrograms", Long.class);
            method.setAccessible(true);
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> selectedPrograms = (List<Map<String, Object>>) method.invoke(patientService, patientId);
            
            // Get available programs (this could also be cached)
            java.lang.reflect.Method availableMethod = patientService.getClass()
                .getDeclaredMethod("getAllAvailablePrograms");
            availableMethod.setAccessible(true);
            
            @SuppressWarnings("unchecked")
            Map<String, Object> availablePrograms = (Map<String, Object>) availableMethod.invoke(patientService);
            
            // Combine available and selected programs
            availablePrograms.put("selectedPrograms", selectedPrograms);
            
            return availablePrograms;
            
        } catch (Exception e) {
            log.error("Error getting programs for patient {}", patientId, e);
            return Map.of();
        }
    }
}
