scalar Long
scalar Map

type Query {
    getPatientsForNurseV2(
        orgId: Long!
        careGiverId: String!
        roleType: String
        pageNumber: Int = 0
        pageSize: Int = 1000
        authToken: String
    ): PatientGCInfos
}

type PatientGCInfos {
    patientInfo: [PatientCGInfo]
    count: Long
    nurseId: String
    responseCode: String
    responseMessage: String
    status: String
    responseType: String
}

type PatientCGInfo {
    patientName: String
    patientId: String
    imeiNo: String
    address: String
    phone: String
    visitReason: String
    image: String
    visitShift: String
    gpsStatus: String
    trackingStatus: String
    radius: String
    latLong: String
    trackLatLong: String
    reachableStatus: Boolean
    dob: String
    connectingDevice: String
    programs: Map
}

type Program {
    programId: Long
    programName: String
}

type ProgramInfo {
    availablePrograms: [Program]
    selectedPrograms: [Map]
}
