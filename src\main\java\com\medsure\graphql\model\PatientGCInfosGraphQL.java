package com.medsure.graphql.model;

import java.util.List;

public class PatientGCInfosGraphQL {
    
    private List<PatientCGInfoGraphQL> patientInfo;
    private Long count;
    private String nurseId;
    private String responseCode;
    private String responseMessage;
    private String status;
    private String responseType;
    
    public PatientGCInfosGraphQL() {}
    
    public List<PatientCGInfoGraphQL> getPatientInfo() {
        return patientInfo;
    }
    
    public void setPatientInfo(List<PatientCGInfoGraphQL> patientInfo) {
        this.patientInfo = patientInfo;
    }
    
    public Long getCount() {
        return count;
    }
    
    public void setCount(Long count) {
        this.count = count;
    }
    
    public String getNurseId() {
        return nurseId;
    }
    
    public void setNurseId(String nurseId) {
        this.nurseId = nurseId;
    }
    
    public String getResponseCode() {
        return responseCode;
    }
    
    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }
    
    public String getResponseMessage() {
        return responseMessage;
    }
    
    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getResponseType() {
        return responseType;
    }
    
    public void setResponseType(String responseType) {
        this.responseType = responseType;
    }
}
