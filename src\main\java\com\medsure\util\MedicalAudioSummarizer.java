package com.medsure.util;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import com.google.api.gax.longrunning.OperationFuture;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.speech.v1.LongRunningRecognizeMetadata;
import com.google.cloud.speech.v1.LongRunningRecognizeResponse;
import com.google.cloud.speech.v1.RecognitionAudio;
import com.google.cloud.speech.v1.RecognitionConfig;
import com.google.cloud.speech.v1.SpeechClient;
import com.google.cloud.speech.v1.SpeechRecognitionAlternative;
import com.google.cloud.speech.v1.SpeechRecognitionResult;
import com.google.cloud.speech.v1.SpeechSettings;
import com.google.cloud.vertexai.VertexAI;
import com.google.cloud.vertexai.api.Content;
import com.google.cloud.vertexai.api.GenerateContentResponse;
import com.google.cloud.vertexai.api.GenerationConfig;
import com.google.cloud.vertexai.generativeai.GenerativeModel;
import com.google.cloud.vertexai.generativeai.ResponseStream;
import com.google.protobuf.ByteString;
import com.medsure.model.WatchrxAudio;
import com.medsure.service.impl.PatientServiceImpl;

public class MedicalAudioSummarizer {
    private static final Logger logger = LoggerFactory.getLogger(PatientServiceImpl.class);
    
    private static final Pattern FOOTER_PATTERN = Pattern.compile("— Time Spent: \\d+ minutes \\| Escalation: (EMERGENT|URGENT|ROUTINE|NONE)$");
    private static final Pattern NUMBER_PATTERN = Pattern.compile("\\b(\\d{2,4})\\b");
    private static final Pattern QUOTE_PATTERN = Pattern.compile("\"([^\"]{3,200})\"");
    
    private static final List<String> FORBIDDEN_PHRASES = Arrays.asList(
        "suggestive of", "likely", "rule out", "possible infection", 
        "diagnosis", "prognosis", "recommend changing medication"
    );
    
    public enum Escalation {
        EMERGENT, URGENT, ROUTINE, NONE
    }
    
    public static class ValidationException extends Exception {
        public ValidationException(String message) {
            super(message);
        }
    }

    public String summarizeData(String audioPath, WatchrxAudio audio) throws Exception {
        String projectId = "watchrx-1007";
        Resource resource = new ClassPathResource("secrets/watchrx-1007-cecd894ddd24.json");
        File file = resource.getFile();
        String absolutePath = file.getAbsolutePath();
        
        String transcript = transcribeAudio(audioPath, absolutePath);

        audio.setAudioText(transcript.trim());
        String summary = summarizeText(transcript, projectId, absolutePath);
        
        String validationChecksJson = validateSummary(summary, transcript);

        audio.setValidationChecks(validationChecksJson);
        audio.setSummarizeAudioText(summary.trim());
        return summary;
    }

    public static String transcribeAudio(String audioPath, String keyPath) throws Exception {
        SpeechSettings settings = SpeechSettings.newBuilder()
                .setCredentialsProvider(() ->
                        ServiceAccountCredentials.fromStream(new FileInputStream(keyPath)))
                .build();

        try (SpeechClient speechClient = SpeechClient.create(settings)) {
            RecognitionConfig config = RecognitionConfig.newBuilder()
                    .setEncoding(RecognitionConfig.AudioEncoding.LINEAR16)
                    .setSampleRateHertz(16000)
                    .setLanguageCode("en-US")
                    .setModel("medical")
                    .setEnableAutomaticPunctuation(true)
                    .setUseEnhanced(true)
                    .build();

            RecognitionAudio audio;

            if (audioPath.startsWith("gs://")) {
                audio = RecognitionAudio.newBuilder()
                        .setUri(audioPath)
                        .build();
            } else {
                ByteString audioBytes;

                if (audioPath.startsWith("https://")) {
                    URL url = new URL(audioPath);
                    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("GET");

                    try (InputStream inputStream = connection.getInputStream();
                         ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {

                        byte[] data = new byte[4096];
                        int nRead;
                        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                            buffer.write(data, 0, nRead);
                        }
                        audioBytes = ByteString.copyFrom(buffer.toByteArray());
                    }

                } else {
                    audioBytes = ByteString.copyFrom(Files.readAllBytes(Path.of(audioPath)));
                }

                audio = RecognitionAudio.newBuilder()
                        .setContent(audioBytes)
                        .build();
            }

            // Use LongRunningRecognize for all inputs
            OperationFuture<LongRunningRecognizeResponse, LongRunningRecognizeMetadata> response =
                    speechClient.longRunningRecognizeAsync(config, audio);

            LongRunningRecognizeResponse result = response.get(5, TimeUnit.MINUTES);

            StringBuilder transcript = new StringBuilder();
            for (SpeechRecognitionResult res : result.getResultsList()) {
                for (SpeechRecognitionAlternative alt : res.getAlternativesList()) {
                    transcript.append(alt.getTranscript()).append(" ");
                }
            }

            String finalTranscript = transcript.toString().trim();

            return finalTranscript;
        }
    }

    public static String summarizeText(String transcript, String projectId, String keyPath) throws Exception {
        String modelName = "gemini-2.0-flash-exp";

        try (VertexAI vertexAI = new VertexAI(projectId, "us-central1")) {
            GenerationConfig generationConfig = GenerationConfig.newBuilder()
                    .setTemperature(0.1f)
                    .setMaxOutputTokens(2000)
                    .setTopP(0.8f)
                    .build();

            GenerativeModel model = new GenerativeModel.Builder()
                    .setModelName(modelName)
                    .setVertexAi(vertexAI)
                    .setGenerationConfig(generationConfig)
                    .build();

            logger.info("Sending prompt to Gemini model: " + modelName);
            String medicalPrompt = buildPrompt(transcript);

            Content promptContent = Content.newBuilder().setRole("user")
                    .addParts(com.google.cloud.vertexai.api.Part.newBuilder().setText(medicalPrompt).build())
                    .build();

            ResponseStream<GenerateContentResponse> responseStream = model.generateContentStream(promptContent);
            StringBuilder sb = new StringBuilder();
            for (GenerateContentResponse chunk : responseStream) {
                if (chunk.getCandidatesCount() > 0) {
                    for (com.google.cloud.vertexai.api.Part part : chunk.getCandidates(0).getContent().getPartsList()) {
                        if (part.hasText()) {
                            sb.append(part.getText());
                        }
                    }
                }
            }
            
            logger.info("FINAL_STRING_****{}", sb.toString());

            String finalResponse = sb.toString().trim();
            
            return finalResponse;
        } catch (IOException e) {
            System.err.println("Error interacting with Vertex AI: " + e.getMessage());
            return e.getMessage();
        }
    }
    
    private static String buildPrompt(String transcript) {
        return String.format("""
            You are a Certified Medical Assistant (CMA) serving as a remote Care Manager for Chronic Care Management (CCM) and Remote Patient Monitoring (RPM), working under the supervision of the ordering provider. Craft a clear, HIPAA‑compliant, present‑tense clinical-encounter summary from the following patient‑caregiver conversation transcript.

            Documentation style & tone
                • Professional, concise, empathetic, and evidence-based.
                • Use standard medical terminology while avoiding jargon that the care team would need to "translate."  
                • Write a single flowing narrative paragraph (no bullet lists) that reads naturally to a clinician.  
                • **Do NOT diagnose, interpret lab data, alter the plan of care, or make independent clinical decisions.**

            Mandatory narrative elements
                1. **Opening context** - Who initiated the call/visit, when, and why.  
                2. **Patient quotations** - Integrate significant direct quotes for clarity ("…") and correct grammar.  
                3. **Current status** - Symptoms, self-reported vital signs (highlight abnormal values in quotes), device readings, and medication adherence exactly as described.  
                4. **Care-management actions** performed during the encounter (education reinforced, care coordination, referrals arranged, etc.).  
                5. **Barriers to care** identified (e.g., transportation, cost, knowledge gaps).  
                6. **Escalation level** - Label as **EMERGENT, URGENT, or ROUTINE** *only if criteria are met*, with brief reasoning:  
                    • **EMERGENT** BP > 180/120, BG < 50 or > 400 mg/dL, severe SOB, chest pain, syncope, acute neuro change, etc.  
                    • **URGENT** BP ≥ 160/100 on repeated readings, BG 300-400 mg/dL with symptoms, 2-5 lb CHF weight gain in 2-3 days, escalating COPD symptoms, missed dialysis, etc.  
                    • **ROUTINE**  Mild deviations or questions that warrant provider review within 24-48 h.  
                7. **Follow-up plan & next steps** exactly as instructed or agreed upon.  
                8. **Time spent** - Document "20 minutes" (or actual reported CCM time ≥ 20 min).  
                9. **Provider-review items** - Note any discrepancies or points requiring the supervising provider's attention.

            Strict content limits
                • Document only what the patient or caregiver actually reported; do **not** add inferences or external data.  
                • Exclude personal opinions, diagnoses, prognoses, and any content not present in the transcript.

            Output format
                Write **one cohesive paragraph**, then add a single line at the end:  
                — Time Spent: XX minutes | Escalation: [EMERGENT/URGENT/ROUTINE or NONE]

            Patient-Caregiver Conversation Transcript:
            %s""", transcript);
    }

    private static String validateSummary(String summary, String transcript) {

        if (summary == null || summary.trim().isEmpty()) {
            return "{\"validationError\":\"Summary is null or empty\",\"overallValid\":\"false\"}";
        }

        StringBuilder jsonBuilder = new StringBuilder();
        jsonBuilder.append("{");

        try {
            // 1. Footer + basic format validation
            boolean hasValidFooter = FOOTER_PATTERN.matcher(summary).find();
            jsonBuilder.append("\"hasValidFooter\":\"").append(hasValidFooter).append("\",");

            int footerIndex = summary.lastIndexOf("— Time Spent");
            if (footerIndex == -1) {
                footerIndex = summary.length();
            }

            String body = summary.substring(0, footerIndex);

            // 2. Single paragraph validation
            int newlineCount = body.length() - body.replace("\n", "").length();
            boolean isSingleParagraph = newlineCount <= 1;
            jsonBuilder.append("\"isSingleParagraph\":\"").append(isSingleParagraph).append("\",");

            // 3. Length validation
            boolean isValidLength = summary.length() <= 4096;
            jsonBuilder.append("\"isValidLength\":\"").append(isValidLength).append("\",");

            // 4. Quote validation (reuse existing helper method)
            String quoteIssue = checkQuotes(body, transcript);
            boolean hasValidQuotes = quoteIssue == null;
            jsonBuilder.append("\"hasValidQuotes\":\"").append(hasValidQuotes).append("\",");

            // 5. Number validation (reuse existing helper method)
            String numberIssue = checkNumbers(body, transcript);
            boolean hasValidNumbers = numberIssue == null;
            jsonBuilder.append("\"hasValidNumbers\":\"").append(hasValidNumbers).append("\",");

            // 6. Forbidden phrases check
            boolean hasNoForbiddenPhrases = true;
            for (String phrase : FORBIDDEN_PHRASES) {
                if (body.toLowerCase().contains(phrase.toLowerCase())) {
                    hasNoForbiddenPhrases = false;
                    break;
                }
            }
            jsonBuilder.append("\"hasNoForbiddenPhrases\":\"").append(hasNoForbiddenPhrases).append("\",");

            boolean overallValid = hasValidFooter && isSingleParagraph && isValidLength &&
                                  hasValidQuotes && hasValidNumbers && hasNoForbiddenPhrases;
            jsonBuilder.append("\"overallValid\":\"").append(overallValid).append("\"");

        } catch (Exception e) {
            logger.error("Summary validation error: {}", e.getMessage());
            jsonBuilder.append("\"validationError\":\"").append(e.getMessage()).append("\",");
            jsonBuilder.append("\"overallValid\":\"false\"");
        }

        jsonBuilder.append("}");

        String validationJson = jsonBuilder.toString();

        return validationJson;
    }
    
    private static String checkQuotes(String body, String transcript) {
        try {
            Matcher matcher = QUOTE_PATTERN.matcher(body);
            while (matcher.find()) {
                String quote = matcher.group(1).trim();
                String normalizedQuote = quote.replaceAll("\\s+", " ").toLowerCase();
                String normalizedTranscript = transcript.toLowerCase();
                
                if (normalizedQuote.length() >= 5 && !normalizedTranscript.contains(normalizedQuote)) {
                    return "Quote not in transcript";
                }
            }
            return null;
        } catch (Exception e) {
            logger.warn("Quote check error: {}", e.getMessage());
            return null;
        }
    }
    
    private static String checkNumbers(String body, String transcript) {
        try {
            Set<String> transcriptNumbers = new HashSet<>();
            Matcher transcriptMatcher = NUMBER_PATTERN.matcher(transcript);
            while (transcriptMatcher.find()) {
                transcriptNumbers.add(transcriptMatcher.group(1));
            }

            Matcher bodyMatcher = NUMBER_PATTERN.matcher(body);
            while (bodyMatcher.find()) {
                String number = bodyMatcher.group(1);
                if (Integer.parseInt(number) > 10 && !transcriptNumbers.contains(number)) {
                    return "Number " + number + " not in transcript";
                }
            }
            
            return null;
        } catch (Exception e) {
            logger.warn("Number check error: {}", e.getMessage());
            return null;
        }
    }
}