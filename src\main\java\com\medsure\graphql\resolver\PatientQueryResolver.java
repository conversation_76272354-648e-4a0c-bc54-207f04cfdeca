package com.medsure.graphql.resolver;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.stereotype.Controller;

import com.medsure.ui.entity.caregiver.common.CareGiverDetails;
import com.medsure.ui.entity.caregiver.response.PatientGCInfos;
import com.medsure.ui.service.caregiver.CareGiverService;

@Controller
public class PatientQueryResolver {

    private static final Logger log = LoggerFactory.getLogger(PatientQueryResolver.class);

    @Autowired
    private CareGiverService careGiverService;

    @QueryMapping
    public PatientGCInfos getPatientsForNurseV2(
            @Argument Long orgId,
            @Argument String careGiverId,
            @Argument(name = "roleType") String roleType,
            @Argument(name = "pageNumber") Integer pageNumber,
            @Argument(name = "pageSize") Integer pageSize,
            @Argument(name = "authToken") String authToken) {

        log.info("GraphQL Query: getPatientsForNurseV2 - orgId: {}, careGiverId: {}, roleType: {}, pageNumber: {}, pageSize: {}",
                orgId, careGiverId, roleType, pageNumber, pageSize);

        try {
            // Create CareGiverDetails request object (reusing existing model)
            CareGiverDetails request = new CareGiverDetails();
            request.setOrgId(orgId);
            request.setCareGiverId(careGiverId);
            request.setRoleType(roleType);
            request.setPageNumber(pageNumber != null ? pageNumber : 0);
            request.setPageSize(pageSize != null ? pageSize : 1000);
            request.setAuthToken(authToken);

            // Call existing service method directly - same authentication as REST API
            // (CareGiverService doesn't enforce authentication either)
            return careGiverService.getPatientsForNurseV2(request);

        } catch (Exception e) {
            log.error("Error in getPatientsForNurseV2 GraphQL query", e);

            // Return error response using existing model
            PatientGCInfos errorResponse = new PatientGCInfos();
            errorResponse.setResponseCode("500");
            errorResponse.setResponseMessage("Internal server error: " + e.getMessage());
            errorResponse.setStatus("error");
            errorResponse.setResponseType("0");

            return errorResponse;
        }
    }
}
