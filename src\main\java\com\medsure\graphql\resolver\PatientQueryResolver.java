package com.medsure.graphql.resolver;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.stereotype.Controller;

import com.medsure.graphql.model.PatientGCInfosGraphQL;
import com.medsure.graphql.service.PatientGraphQLService;

import graphql.schema.DataFetchingEnvironment;

@Controller
public class PatientQueryResolver {
    
    private static final Logger log = LoggerFactory.getLogger(PatientQueryResolver.class);
    
    @Autowired
    private PatientGraphQLService patientGraphQLService;
    
    @QueryMapping
    public PatientGCInfosGraphQL getPatientsForNurse(
            @Argument Long orgId,
            @Argument String careGiverId,
            @Argument(name = "roleType") String roleType,
            @Argument(name = "pageNumber", defaultValue = "0") Integer pageNumber,
            @Argument(name = "pageSize", defaultValue = "1000") Integer pageSize,
            DataFetchingEnvironment environment) {
        
        log.info("GraphQL Query: getPatientsForNurse - orgId: {}, careGiverId: {}, roleType: {}, pageNumber: {}, pageSize: {}", 
                orgId, careGiverId, roleType, pageNumber, pageSize);
        
        try {
            return patientGraphQLService.getPatientsForNurse(
                orgId, careGiverId, roleType, pageNumber, pageSize, environment);
        } catch (Exception e) {
            log.error("Error in getPatientsForNurse GraphQL query", e);
            
            // Return error response
            PatientGCInfosGraphQL errorResponse = new PatientGCInfosGraphQL();
            errorResponse.setResponseCode("500");
            errorResponse.setResponseMessage("Internal server error: " + e.getMessage());
            errorResponse.setStatus("error");
            errorResponse.setResponseType("0");
            
            return errorResponse;
        }
    }
}
