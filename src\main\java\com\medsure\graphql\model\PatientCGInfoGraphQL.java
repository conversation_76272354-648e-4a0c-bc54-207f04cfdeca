package com.medsure.graphql.model;

import java.util.Map;

public class PatientCGInfoGraphQL {
    
    private String patientName;
    private String patientId;
    private String imeiNo;
    private String address;
    private String phone;
    private String visitReason;
    private String image;
    private String visitShift;
    private String gpsStatus;
    private String trackingStatus;
    private String radius;
    private String latLong;
    private String trackLatLong;
    private Boolean reachableStatus;
    private String dob;
    private String connectingDevice;
    private Map<String, Object> programs;
    
    public PatientCGInfoGraphQL() {}
    
    public String getPatientName() {
        return patientName;
    }
    
    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }
    
    public String getPatientId() {
        return patientId;
    }
    
    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }
    
    public String getImeiNo() {
        return imeiNo;
    }
    
    public void setImeiNo(String imeiNo) {
        this.imeiNo = imeiNo;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getVisitReason() {
        return visitReason;
    }
    
    public void setVisitReason(String visitReason) {
        this.visitReason = visitReason;
    }
    
    public String getImage() {
        return image;
    }
    
    public void setImage(String image) {
        this.image = image;
    }
    
    public String getVisitShift() {
        return visitShift;
    }
    
    public void setVisitShift(String visitShift) {
        this.visitShift = visitShift;
    }
    
    public String getGpsStatus() {
        return gpsStatus;
    }
    
    public void setGpsStatus(String gpsStatus) {
        this.gpsStatus = gpsStatus;
    }
    
    public String getTrackingStatus() {
        return trackingStatus;
    }
    
    public void setTrackingStatus(String trackingStatus) {
        this.trackingStatus = trackingStatus;
    }
    
    public String getRadius() {
        return radius;
    }
    
    public void setRadius(String radius) {
        this.radius = radius;
    }
    
    public String getLatLong() {
        return latLong;
    }
    
    public void setLatLong(String latLong) {
        this.latLong = latLong;
    }
    
    public String getTrackLatLong() {
        return trackLatLong;
    }
    
    public void setTrackLatLong(String trackLatLong) {
        this.trackLatLong = trackLatLong;
    }
    
    public Boolean getReachableStatus() {
        return reachableStatus;
    }
    
    public void setReachableStatus(Boolean reachableStatus) {
        this.reachableStatus = reachableStatus;
    }
    
    public String getDob() {
        return dob;
    }
    
    public void setDob(String dob) {
        this.dob = dob;
    }
    
    public String getConnectingDevice() {
        return connectingDevice;
    }
    
    public void setConnectingDevice(String connectingDevice) {
        this.connectingDevice = connectingDevice;
    }
    
    public Map<String, Object> getPrograms() {
        return programs;
    }
    
    public void setPrograms(Map<String, Object> programs) {
        this.programs = programs;
    }
}
