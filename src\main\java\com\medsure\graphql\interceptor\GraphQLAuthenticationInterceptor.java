package com.medsure.graphql.interceptor;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.server.WebGraphQlInterceptor;
import org.springframework.graphql.server.WebGraphQlRequest;
import org.springframework.graphql.server.WebGraphQlResponse;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.medsure.ui.entity.server.UserVO;
import com.medsure.ui.util.AESUtil;
import com.medsure.ui.util.JwtTokenUtil;

import reactor.core.publisher.Mono;

@Component
public class GraphQLAuthenticationInterceptor implements WebGraphQlInterceptor {
    
    private static final Logger log = LoggerFactory.getLogger(GraphQLAuthenticationInterceptor.class);
    
    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    
    @Override
    public Mono<WebGraphQlResponse> intercept(WebGraphQlRequest request, Chain chain) {
        try {
            // Check for authentication similar to existing REST endpoints
            String authToken = request.getHeaders().getFirst("auth-token");
            String userDetails = request.getHeaders().getFirst("userdetails");
            String apiKey = request.getHeaders().getFirst("watchrx-api-access-key");
            
            // Option 1: JWT Token authentication (like AuthenticationInterceptor)
            if (authToken != null && userDetails != null) {
                String userName = jwtTokenUtil.getUsernameFromToken(authToken);
                if (userName != null) {
                    UserVO user = new ObjectMapper().readValue(
                        AESUtil.decryptText(userDetails, "patient@watCRX"), UserVO.class);
                    if (user != null) {
                        // Authentication successful, add user to context
                        request.configureExecutionInput((executionInput, builder) -> 
                            builder.graphQLContext(context -> context.put("user", user)).build());
                        return chain.next(request);
                    }
                }
            }
            
            // Option 2: API Key authentication (like APISecurityConfig)
            if (apiKey != null && "928ded27-570c-4ac5-90b1-1655d1389a0e".equals(apiKey)) {
                // API key authentication successful
                return chain.next(request);
            }
            
            // Option 3: For development/testing - allow authToken in GraphQL variables
            // This allows passing authToken as a GraphQL query parameter
            if (request.getDocument() != null) {
                // Extract authToken from GraphQL variables if present
                Object variables = request.getVariables();
                if (variables instanceof java.util.Map) {
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, Object> variableMap = (java.util.Map<String, Object>) variables;
                    String tokenFromVariables = (String) variableMap.get("authToken");
                    if (tokenFromVariables != null) {
                        // For now, we'll allow this for GraphQL queries
                        // In production, you might want to validate this token
                        return chain.next(request);
                    }
                }
            }
            
            // Authentication failed
            log.warn("GraphQL request failed authentication");
            return Mono.error(new RuntimeException("Authentication required"));
            
        } catch (Exception e) {
            log.error("Error in GraphQL authentication", e);
            return Mono.error(new RuntimeException("Authentication error: " + e.getMessage()));
        }
    }
}
